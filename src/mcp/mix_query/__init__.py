from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Type, Optional, List, Dict, Any

from langchain_core.callbacks import CallbackManagerForToolRun
from langchain_core.documents import Document
from langchain_core.tools import BaseTool
from mcp import ClientSession
from mcp.client.sse import sse_client
from mcp.types import TextContent, ImageContent, EmbeddedResource
from pydantic import Field, BaseModel

from src.helper.client.model import get_chat_model
from src.log import logger
from src.mcp.chain import create_milvus_qa_chain
from src.mcp.retriever import HybridMilvusRetriever
from .aggregator import ResultAggregator
from .intent_parser import IntentParser
from ...helper.client.milvus import hybrid_search_optimized


class MixQueryArgs(BaseModel):
    query: str = Field(..., description="查询的内容描述")


class MixQueryTool(BaseTool):
    """融合查询工具，同时查询知识库和CMDB资源信息"""

    name: str = "mix_query"
    args_schema: Type[BaseModel] = MixQueryArgs
    return_direct: bool = True
    description: str = "融合查询工具，可以同时查询知识库文档和CMDB资源信息，支持复杂的用户意图分析和结果聚合"
    collection_name: str

    def _run(
            self, query: str, run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> str:
        """
        执行融合查询的主要逻辑

        Args:
            query: 用户查询问题
            run_manager: 回调管理器

        Returns:
            聚合后的查询结果
        """
        try:
            logger.info(f"开始融合查询: {query}")

            # 1. 使用大模型分解用户意图
            parsed_intents = self._parse_user_intent(query)
            logger.info(f"解析到的意图: {parsed_intents}")

            # 2. 并发查询知识库和CMDB
            doc_results, resource_results = self._concurrent_query(parsed_intents)

            # 3. 聚合处理结果
            final_answer = self._aggregate_results(query, parsed_intents, doc_results, resource_results)

            logger.info(f"融合查询完成")
            return final_answer

        except Exception as e:
            logger.error(f"融合查询失败: {e}")
            return f"查询过程中出现错误: {str(e)}"

    def _parse_user_intent(self, query: str) -> List[Dict[str, Any]]:
        """解析用户意图"""
        intent_parser: IntentParser = IntentParser()

        return intent_parser.parse(query)

    def _concurrent_query(self, parsed_intents: List[Dict[str, Any]]) -> tuple[List[Dict], List[Dict]]:
        """并发查询知识库和CMDB"""
        doc_results = []
        resource_results = []

        with ThreadPoolExecutor(max_workers=4) as executor:
            # 提交所有查询任务
            future_to_type = {}

            for intent in parsed_intents:
                future = executor.submit(self._query_knowledge_base, intent)
                future_to_type[future] = ('doc', intent)

                if intent.get('resources'):
                    for resource in intent['resources']:
                        future = executor.submit(self._query_cmdb, resource)
                        future_to_type[future] = ('resource', resource)

            # 收集结果
            for future in as_completed(future_to_type):
                query_type, query_info = future_to_type[future]
                try:
                    result = future.result()
                    if query_type == 'doc':
                        if result:
                            doc_results.append({
                                'intent': query_info,
                                'result': result
                            })
                    else:  # resource
                        if result:
                            resource_results.append({
                                'resource': query_info,
                                'result': result
                            })
                except Exception as e:
                    logger.error(f"查询失败 {query_type} - {query_info}: {e}")

        return doc_results, resource_results

    def _query_knowledge_base(self, intent: Dict[str, Any]) -> List[Document]:
        """查询知识库"""
        try:
            query_text = intent['intention']
            keywords = intent['keywords']
            return hybrid_search_optimized(query_text, self.collection_name, keywords)
        except Exception as e:
            logger.error(f"知识库查询失败: {e}")
            return []

    async def _query_cmdb(self, resource: str) -> list[TextContent | ImageContent | EmbeddedResource] | None:
        """查询CMDB资源"""
        try:
            async with sse_client(url="https://tt-telemetry.ttyuyin.com") as (read, write):
                session = ClientSession(read, write)
                async with session:
                    await session.initialize()
                    result = await session.call_tool("query_cmdb", {"query": resource})
                    logger.info(f"query_cmdb result={result.content}")
                    return result.content
        except Exception as e:
            logger.error(f"CMDB查询失败 {resource}: {e}")
            return None

    def _aggregate_results(self, original_query: str, parsed_intents: List[Dict],
                           doc_results: List[Dict], resource_results: List[Dict]) -> str:
        """聚合查询结果"""
        aggregator = ResultAggregator()
        return aggregator.aggregate(
            original_query=original_query,
            parsed_intents=parsed_intents,
            doc_results=doc_results,
            resource_results=resource_results
        )
