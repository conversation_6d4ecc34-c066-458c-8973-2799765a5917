from typing import List, Any, Optional, Dict

from langchain.schema import Document, BaseRetriever
from langchain.callbacks.manager import CallbackManagerForRetrieverRun

from src.helper.client.milvus import hybrid_search_optimized


class HybridMilvusRetriever(BaseRetriever):
    """Retriever that uses Milvus hybrid search."""

    collection_name: str
    top_k: int

    def __init__(
            self,
            collection_name: str,
            top_k: int = 4,
    ):
        """Initialize the HybridMilvusRetriever."""
        super().__init__(collection_name=collection_name, top_k=top_k)
        self.collection_name = collection_name
        self.top_k = top_k

    def get_relevant_documents(
            self,
            query: str,
            *,
            callbacks: Optional[CallbackManagerForRetrieverRun] = None,
            **kwargs: Any,
    ) -> List[Document]:
        """Get documents relevant to the query."""
        return hybrid_search_optimized(
            query,
            self.collection_name,
        )
