[project]
name = "ops-brain-knowledges"
version = "0.1.0"
description = ""
authors = [
    {name = "Your Name",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10,<4.0"
dependencies = [
    "psycopg (>=3.2.5,<4.0.0)",
    "fastapi (>=0.115.11,<0.116.0)",
    "apscheduler (>=3.11.0,<4.0.0)",
    "uvicorn (>=0.34.0,<0.35.0)",
    "loguru (>=0.7.3,<0.8.0)",
    "lark-oapi (>=1.4.10,<2.0.0)",
    "tenacity (>=9.0.0,<10.0.0)",
    "langchain-community (>=0.3.19,<0.4.0)",
    "lark2md @ git+ssh://**********************/yunwei-infra/lark2md.git",
    "sqlmodel (>=0.0.23,<0.0.24)",
    "langchain-milvus (>=0.1.8,<0.2.0)",
    "langchain-openai (>=0.3.7,<0.4.0)",
    "langchain-huggingface (>=0.1.2,<0.2.0)",
    "rank-bm25 (>=0.2.2,<0.3.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"


[tool.poetry]
package-mode = false